# -*- coding: utf-8 -*-
# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
import inspect
import json
import logging as std_logging
import pickle
from typing import Awaitable, Callable, Dict, Optional, Sequence, Tuple, Union
import warnings

from google.api_core import exceptions as core_exceptions
from google.api_core import gapic_v1, grpc_helpers_async
from google.api_core import retry_async as retries
from google.auth import credentials as ga_credentials  # type: ignore
from google.auth.transport.grpc import SslCredentials  # type: ignore
from google.longrunning import operations_pb2  # type: ignore
from google.protobuf import empty_pb2  # type: ignore
from google.protobuf.json_format import MessageToJson
import google.protobuf.message
import grpc  # type: ignore
from grpc.experimental import aio  # type: ignore
import proto  # type: ignore

from google.ai.generativelanguage_v1alpha.types import retriever, retriever_service

from .base import DEFAULT_CLIENT_INFO, RetrieverServiceTransport
from .grpc import RetrieverServiceGrpcTransport

try:
    from google.api_core import client_logging  # type: ignore

    CLIENT_LOGGING_SUPPORTED = True  # pragma: NO COVER
except ImportError:  # pragma: NO COVER
    CLIENT_LOGGING_SUPPORTED = False

_LOGGER = std_logging.getLogger(__name__)


class _LoggingClientAIOInterceptor(
    grpc.aio.UnaryUnaryClientInterceptor
):  # pragma: NO COVER
    async def intercept_unary_unary(self, continuation, client_call_details, request):
        logging_enabled = CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
            std_logging.DEBUG
        )
        if logging_enabled:  # pragma: NO COVER
            request_metadata = client_call_details.metadata
            if isinstance(request, proto.Message):
                request_payload = type(request).to_json(request)
            elif isinstance(request, google.protobuf.message.Message):
                request_payload = MessageToJson(request)
            else:
                request_payload = f"{type(request).__name__}: {pickle.dumps(request)}"

            request_metadata = {
                key: value.decode("utf-8") if isinstance(value, bytes) else value
                for key, value in request_metadata
            }
            grpc_request = {
                "payload": request_payload,
                "requestMethod": "grpc",
                "metadata": dict(request_metadata),
            }
            _LOGGER.debug(
                f"Sending request for {client_call_details.method}",
                extra={
                    "serviceName": "google.ai.generativelanguage.v1alpha.RetrieverService",
                    "rpcName": str(client_call_details.method),
                    "request": grpc_request,
                    "metadata": grpc_request["metadata"],
                },
            )
        response = await continuation(client_call_details, request)
        if logging_enabled:  # pragma: NO COVER
            response_metadata = await response.trailing_metadata()
            # Convert gRPC metadata `<class 'grpc.aio._metadata.Metadata'>` to list of tuples
            metadata = (
                dict([(k, str(v)) for k, v in response_metadata])
                if response_metadata
                else None
            )
            result = await response
            if isinstance(result, proto.Message):
                response_payload = type(result).to_json(result)
            elif isinstance(result, google.protobuf.message.Message):
                response_payload = MessageToJson(result)
            else:
                response_payload = f"{type(result).__name__}: {pickle.dumps(result)}"
            grpc_response = {
                "payload": response_payload,
                "metadata": metadata,
                "status": "OK",
            }
            _LOGGER.debug(
                f"Received response to rpc {client_call_details.method}.",
                extra={
                    "serviceName": "google.ai.generativelanguage.v1alpha.RetrieverService",
                    "rpcName": str(client_call_details.method),
                    "response": grpc_response,
                    "metadata": grpc_response["metadata"],
                },
            )
        return response


class RetrieverServiceGrpcAsyncIOTransport(RetrieverServiceTransport):
    """gRPC AsyncIO backend transport for RetrieverService.

    An API for semantic search over a corpus of user uploaded
    content.

    This class defines the same methods as the primary client, so the
    primary client can load the underlying transport implementation
    and call it.

    It sends protocol buffers over the wire using gRPC (which is built on
    top of HTTP/2); the ``grpcio`` package must be installed.
    """

    _grpc_channel: aio.Channel
    _stubs: Dict[str, Callable] = {}

    @classmethod
    def create_channel(
        cls,
        host: str = "generativelanguage.googleapis.com",
        credentials: Optional[ga_credentials.Credentials] = None,
        credentials_file: Optional[str] = None,
        scopes: Optional[Sequence[str]] = None,
        quota_project_id: Optional[str] = None,
        **kwargs,
    ) -> aio.Channel:
        """Create and return a gRPC AsyncIO channel object.
        Args:
            host (Optional[str]): The host for the channel to use.
            credentials (Optional[~.Credentials]): The
                authorization credentials to attach to requests. These
                credentials identify this application to the service. If
                none are specified, the client will attempt to ascertain
                the credentials from the environment.
            credentials_file (Optional[str]): A file with credentials that can
                be loaded with :func:`google.auth.load_credentials_from_file`.
            scopes (Optional[Sequence[str]]): A optional list of scopes needed for this
                service. These are only used when credentials are not specified and
                are passed to :func:`google.auth.default`.
            quota_project_id (Optional[str]): An optional project to use for billing
                and quota.
            kwargs (Optional[dict]): Keyword arguments, which are passed to the
                channel creation.
        Returns:
            aio.Channel: A gRPC AsyncIO channel object.
        """

        return grpc_helpers_async.create_channel(
            host,
            credentials=credentials,
            credentials_file=credentials_file,
            quota_project_id=quota_project_id,
            default_scopes=cls.AUTH_SCOPES,
            scopes=scopes,
            default_host=cls.DEFAULT_HOST,
            **kwargs,
        )

    def __init__(
        self,
        *,
        host: str = "generativelanguage.googleapis.com",
        credentials: Optional[ga_credentials.Credentials] = None,
        credentials_file: Optional[str] = None,
        scopes: Optional[Sequence[str]] = None,
        channel: Optional[Union[aio.Channel, Callable[..., aio.Channel]]] = None,
        api_mtls_endpoint: Optional[str] = None,
        client_cert_source: Optional[Callable[[], Tuple[bytes, bytes]]] = None,
        ssl_channel_credentials: Optional[grpc.ChannelCredentials] = None,
        client_cert_source_for_mtls: Optional[Callable[[], Tuple[bytes, bytes]]] = None,
        quota_project_id: Optional[str] = None,
        client_info: gapic_v1.client_info.ClientInfo = DEFAULT_CLIENT_INFO,
        always_use_jwt_access: Optional[bool] = False,
        api_audience: Optional[str] = None,
    ) -> None:
        """Instantiate the transport.

        Args:
            host (Optional[str]):
                 The hostname to connect to (default: 'generativelanguage.googleapis.com').
            credentials (Optional[google.auth.credentials.Credentials]): The
                authorization credentials to attach to requests. These
                credentials identify the application to the service; if none
                are specified, the client will attempt to ascertain the
                credentials from the environment.
                This argument is ignored if a ``channel`` instance is provided.
            credentials_file (Optional[str]): A file with credentials that can
                be loaded with :func:`google.auth.load_credentials_from_file`.
                This argument is ignored if a ``channel`` instance is provided.
            scopes (Optional[Sequence[str]]): A optional list of scopes needed for this
                service. These are only used when credentials are not specified and
                are passed to :func:`google.auth.default`.
            channel (Optional[Union[aio.Channel, Callable[..., aio.Channel]]]):
                A ``Channel`` instance through which to make calls, or a Callable
                that constructs and returns one. If set to None, ``self.create_channel``
                is used to create the channel. If a Callable is given, it will be called
                with the same arguments as used in ``self.create_channel``.
            api_mtls_endpoint (Optional[str]): Deprecated. The mutual TLS endpoint.
                If provided, it overrides the ``host`` argument and tries to create
                a mutual TLS channel with client SSL credentials from
                ``client_cert_source`` or application default SSL credentials.
            client_cert_source (Optional[Callable[[], Tuple[bytes, bytes]]]):
                Deprecated. A callback to provide client SSL certificate bytes and
                private key bytes, both in PEM format. It is ignored if
                ``api_mtls_endpoint`` is None.
            ssl_channel_credentials (grpc.ChannelCredentials): SSL credentials
                for the grpc channel. It is ignored if a ``channel`` instance is provided.
            client_cert_source_for_mtls (Optional[Callable[[], Tuple[bytes, bytes]]]):
                A callback to provide client certificate bytes and private key bytes,
                both in PEM format. It is used to configure a mutual TLS channel. It is
                ignored if a ``channel`` instance or ``ssl_channel_credentials`` is provided.
            quota_project_id (Optional[str]): An optional project to use for billing
                and quota.
            client_info (google.api_core.gapic_v1.client_info.ClientInfo):
                The client info used to send a user-agent string along with
                API requests. If ``None``, then default info will be used.
                Generally, you only need to set this if you're developing
                your own client library.
            always_use_jwt_access (Optional[bool]): Whether self signed JWT should
                be used for service account credentials.

        Raises:
            google.auth.exceptions.MutualTlsChannelError: If mutual TLS transport
              creation failed for any reason.
          google.api_core.exceptions.DuplicateCredentialArgs: If both ``credentials``
              and ``credentials_file`` are passed.
        """
        self._grpc_channel = None
        self._ssl_channel_credentials = ssl_channel_credentials
        self._stubs: Dict[str, Callable] = {}

        if api_mtls_endpoint:
            warnings.warn("api_mtls_endpoint is deprecated", DeprecationWarning)
        if client_cert_source:
            warnings.warn("client_cert_source is deprecated", DeprecationWarning)

        if isinstance(channel, aio.Channel):
            # Ignore credentials if a channel was passed.
            credentials = None
            self._ignore_credentials = True
            # If a channel was explicitly provided, set it.
            self._grpc_channel = channel
            self._ssl_channel_credentials = None
        else:
            if api_mtls_endpoint:
                host = api_mtls_endpoint

                # Create SSL credentials with client_cert_source or application
                # default SSL credentials.
                if client_cert_source:
                    cert, key = client_cert_source()
                    self._ssl_channel_credentials = grpc.ssl_channel_credentials(
                        certificate_chain=cert, private_key=key
                    )
                else:
                    self._ssl_channel_credentials = SslCredentials().ssl_credentials

            else:
                if client_cert_source_for_mtls and not ssl_channel_credentials:
                    cert, key = client_cert_source_for_mtls()
                    self._ssl_channel_credentials = grpc.ssl_channel_credentials(
                        certificate_chain=cert, private_key=key
                    )

        # The base transport sets the host, credentials and scopes
        super().__init__(
            host=host,
            credentials=credentials,
            credentials_file=credentials_file,
            scopes=scopes,
            quota_project_id=quota_project_id,
            client_info=client_info,
            always_use_jwt_access=always_use_jwt_access,
            api_audience=api_audience,
        )

        if not self._grpc_channel:
            # initialize with the provided callable or the default channel
            channel_init = channel or type(self).create_channel
            self._grpc_channel = channel_init(
                self._host,
                # use the credentials which are saved
                credentials=self._credentials,
                # Set ``credentials_file`` to ``None`` here as
                # the credentials that we saved earlier should be used.
                credentials_file=None,
                scopes=self._scopes,
                ssl_credentials=self._ssl_channel_credentials,
                quota_project_id=quota_project_id,
                options=[
                    ("grpc.max_send_message_length", -1),
                    ("grpc.max_receive_message_length", -1),
                ],
            )

        self._interceptor = _LoggingClientAIOInterceptor()
        self._grpc_channel._unary_unary_interceptors.append(self._interceptor)
        self._logged_channel = self._grpc_channel
        self._wrap_with_kind = (
            "kind" in inspect.signature(gapic_v1.method_async.wrap_method).parameters
        )
        # Wrap messages. This must be done after self._logged_channel exists
        self._prep_wrapped_messages(client_info)

    @property
    def grpc_channel(self) -> aio.Channel:
        """Create the channel designed to connect to this service.

        This property caches on the instance; repeated calls return
        the same channel.
        """
        # Return the channel from cache.
        return self._grpc_channel

    @property
    def create_corpus(
        self,
    ) -> Callable[[retriever_service.CreateCorpusRequest], Awaitable[retriever.Corpus]]:
        r"""Return a callable for the create corpus method over gRPC.

        Creates an empty ``Corpus``.

        Returns:
            Callable[[~.CreateCorpusRequest],
                    Awaitable[~.Corpus]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "create_corpus" not in self._stubs:
            self._stubs["create_corpus"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/CreateCorpus",
                request_serializer=retriever_service.CreateCorpusRequest.serialize,
                response_deserializer=retriever.Corpus.deserialize,
            )
        return self._stubs["create_corpus"]

    @property
    def get_corpus(
        self,
    ) -> Callable[[retriever_service.GetCorpusRequest], Awaitable[retriever.Corpus]]:
        r"""Return a callable for the get corpus method over gRPC.

        Gets information about a specific ``Corpus``.

        Returns:
            Callable[[~.GetCorpusRequest],
                    Awaitable[~.Corpus]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "get_corpus" not in self._stubs:
            self._stubs["get_corpus"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/GetCorpus",
                request_serializer=retriever_service.GetCorpusRequest.serialize,
                response_deserializer=retriever.Corpus.deserialize,
            )
        return self._stubs["get_corpus"]

    @property
    def update_corpus(
        self,
    ) -> Callable[[retriever_service.UpdateCorpusRequest], Awaitable[retriever.Corpus]]:
        r"""Return a callable for the update corpus method over gRPC.

        Updates a ``Corpus``.

        Returns:
            Callable[[~.UpdateCorpusRequest],
                    Awaitable[~.Corpus]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "update_corpus" not in self._stubs:
            self._stubs["update_corpus"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/UpdateCorpus",
                request_serializer=retriever_service.UpdateCorpusRequest.serialize,
                response_deserializer=retriever.Corpus.deserialize,
            )
        return self._stubs["update_corpus"]

    @property
    def delete_corpus(
        self,
    ) -> Callable[[retriever_service.DeleteCorpusRequest], Awaitable[empty_pb2.Empty]]:
        r"""Return a callable for the delete corpus method over gRPC.

        Deletes a ``Corpus``.

        Returns:
            Callable[[~.DeleteCorpusRequest],
                    Awaitable[~.Empty]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "delete_corpus" not in self._stubs:
            self._stubs["delete_corpus"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/DeleteCorpus",
                request_serializer=retriever_service.DeleteCorpusRequest.serialize,
                response_deserializer=empty_pb2.Empty.FromString,
            )
        return self._stubs["delete_corpus"]

    @property
    def list_corpora(
        self,
    ) -> Callable[
        [retriever_service.ListCorporaRequest],
        Awaitable[retriever_service.ListCorporaResponse],
    ]:
        r"""Return a callable for the list corpora method over gRPC.

        Lists all ``Corpora`` owned by the user.

        Returns:
            Callable[[~.ListCorporaRequest],
                    Awaitable[~.ListCorporaResponse]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "list_corpora" not in self._stubs:
            self._stubs["list_corpora"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/ListCorpora",
                request_serializer=retriever_service.ListCorporaRequest.serialize,
                response_deserializer=retriever_service.ListCorporaResponse.deserialize,
            )
        return self._stubs["list_corpora"]

    @property
    def query_corpus(
        self,
    ) -> Callable[
        [retriever_service.QueryCorpusRequest],
        Awaitable[retriever_service.QueryCorpusResponse],
    ]:
        r"""Return a callable for the query corpus method over gRPC.

        Performs semantic search over a ``Corpus``.

        Returns:
            Callable[[~.QueryCorpusRequest],
                    Awaitable[~.QueryCorpusResponse]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "query_corpus" not in self._stubs:
            self._stubs["query_corpus"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/QueryCorpus",
                request_serializer=retriever_service.QueryCorpusRequest.serialize,
                response_deserializer=retriever_service.QueryCorpusResponse.deserialize,
            )
        return self._stubs["query_corpus"]

    @property
    def create_document(
        self,
    ) -> Callable[
        [retriever_service.CreateDocumentRequest], Awaitable[retriever.Document]
    ]:
        r"""Return a callable for the create document method over gRPC.

        Creates an empty ``Document``.

        Returns:
            Callable[[~.CreateDocumentRequest],
                    Awaitable[~.Document]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "create_document" not in self._stubs:
            self._stubs["create_document"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/CreateDocument",
                request_serializer=retriever_service.CreateDocumentRequest.serialize,
                response_deserializer=retriever.Document.deserialize,
            )
        return self._stubs["create_document"]

    @property
    def get_document(
        self,
    ) -> Callable[
        [retriever_service.GetDocumentRequest], Awaitable[retriever.Document]
    ]:
        r"""Return a callable for the get document method over gRPC.

        Gets information about a specific ``Document``.

        Returns:
            Callable[[~.GetDocumentRequest],
                    Awaitable[~.Document]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "get_document" not in self._stubs:
            self._stubs["get_document"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/GetDocument",
                request_serializer=retriever_service.GetDocumentRequest.serialize,
                response_deserializer=retriever.Document.deserialize,
            )
        return self._stubs["get_document"]

    @property
    def update_document(
        self,
    ) -> Callable[
        [retriever_service.UpdateDocumentRequest], Awaitable[retriever.Document]
    ]:
        r"""Return a callable for the update document method over gRPC.

        Updates a ``Document``.

        Returns:
            Callable[[~.UpdateDocumentRequest],
                    Awaitable[~.Document]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "update_document" not in self._stubs:
            self._stubs["update_document"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/UpdateDocument",
                request_serializer=retriever_service.UpdateDocumentRequest.serialize,
                response_deserializer=retriever.Document.deserialize,
            )
        return self._stubs["update_document"]

    @property
    def delete_document(
        self,
    ) -> Callable[
        [retriever_service.DeleteDocumentRequest], Awaitable[empty_pb2.Empty]
    ]:
        r"""Return a callable for the delete document method over gRPC.

        Deletes a ``Document``.

        Returns:
            Callable[[~.DeleteDocumentRequest],
                    Awaitable[~.Empty]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "delete_document" not in self._stubs:
            self._stubs["delete_document"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/DeleteDocument",
                request_serializer=retriever_service.DeleteDocumentRequest.serialize,
                response_deserializer=empty_pb2.Empty.FromString,
            )
        return self._stubs["delete_document"]

    @property
    def list_documents(
        self,
    ) -> Callable[
        [retriever_service.ListDocumentsRequest],
        Awaitable[retriever_service.ListDocumentsResponse],
    ]:
        r"""Return a callable for the list documents method over gRPC.

        Lists all ``Document``\ s in a ``Corpus``.

        Returns:
            Callable[[~.ListDocumentsRequest],
                    Awaitable[~.ListDocumentsResponse]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "list_documents" not in self._stubs:
            self._stubs["list_documents"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/ListDocuments",
                request_serializer=retriever_service.ListDocumentsRequest.serialize,
                response_deserializer=retriever_service.ListDocumentsResponse.deserialize,
            )
        return self._stubs["list_documents"]

    @property
    def query_document(
        self,
    ) -> Callable[
        [retriever_service.QueryDocumentRequest],
        Awaitable[retriever_service.QueryDocumentResponse],
    ]:
        r"""Return a callable for the query document method over gRPC.

        Performs semantic search over a ``Document``.

        Returns:
            Callable[[~.QueryDocumentRequest],
                    Awaitable[~.QueryDocumentResponse]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "query_document" not in self._stubs:
            self._stubs["query_document"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/QueryDocument",
                request_serializer=retriever_service.QueryDocumentRequest.serialize,
                response_deserializer=retriever_service.QueryDocumentResponse.deserialize,
            )
        return self._stubs["query_document"]

    @property
    def create_chunk(
        self,
    ) -> Callable[[retriever_service.CreateChunkRequest], Awaitable[retriever.Chunk]]:
        r"""Return a callable for the create chunk method over gRPC.

        Creates a ``Chunk``.

        Returns:
            Callable[[~.CreateChunkRequest],
                    Awaitable[~.Chunk]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "create_chunk" not in self._stubs:
            self._stubs["create_chunk"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/CreateChunk",
                request_serializer=retriever_service.CreateChunkRequest.serialize,
                response_deserializer=retriever.Chunk.deserialize,
            )
        return self._stubs["create_chunk"]

    @property
    def batch_create_chunks(
        self,
    ) -> Callable[
        [retriever_service.BatchCreateChunksRequest],
        Awaitable[retriever_service.BatchCreateChunksResponse],
    ]:
        r"""Return a callable for the batch create chunks method over gRPC.

        Batch create ``Chunk``\ s.

        Returns:
            Callable[[~.BatchCreateChunksRequest],
                    Awaitable[~.BatchCreateChunksResponse]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "batch_create_chunks" not in self._stubs:
            self._stubs["batch_create_chunks"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/BatchCreateChunks",
                request_serializer=retriever_service.BatchCreateChunksRequest.serialize,
                response_deserializer=retriever_service.BatchCreateChunksResponse.deserialize,
            )
        return self._stubs["batch_create_chunks"]

    @property
    def get_chunk(
        self,
    ) -> Callable[[retriever_service.GetChunkRequest], Awaitable[retriever.Chunk]]:
        r"""Return a callable for the get chunk method over gRPC.

        Gets information about a specific ``Chunk``.

        Returns:
            Callable[[~.GetChunkRequest],
                    Awaitable[~.Chunk]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "get_chunk" not in self._stubs:
            self._stubs["get_chunk"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/GetChunk",
                request_serializer=retriever_service.GetChunkRequest.serialize,
                response_deserializer=retriever.Chunk.deserialize,
            )
        return self._stubs["get_chunk"]

    @property
    def update_chunk(
        self,
    ) -> Callable[[retriever_service.UpdateChunkRequest], Awaitable[retriever.Chunk]]:
        r"""Return a callable for the update chunk method over gRPC.

        Updates a ``Chunk``.

        Returns:
            Callable[[~.UpdateChunkRequest],
                    Awaitable[~.Chunk]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "update_chunk" not in self._stubs:
            self._stubs["update_chunk"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/UpdateChunk",
                request_serializer=retriever_service.UpdateChunkRequest.serialize,
                response_deserializer=retriever.Chunk.deserialize,
            )
        return self._stubs["update_chunk"]

    @property
    def batch_update_chunks(
        self,
    ) -> Callable[
        [retriever_service.BatchUpdateChunksRequest],
        Awaitable[retriever_service.BatchUpdateChunksResponse],
    ]:
        r"""Return a callable for the batch update chunks method over gRPC.

        Batch update ``Chunk``\ s.

        Returns:
            Callable[[~.BatchUpdateChunksRequest],
                    Awaitable[~.BatchUpdateChunksResponse]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "batch_update_chunks" not in self._stubs:
            self._stubs["batch_update_chunks"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/BatchUpdateChunks",
                request_serializer=retriever_service.BatchUpdateChunksRequest.serialize,
                response_deserializer=retriever_service.BatchUpdateChunksResponse.deserialize,
            )
        return self._stubs["batch_update_chunks"]

    @property
    def delete_chunk(
        self,
    ) -> Callable[[retriever_service.DeleteChunkRequest], Awaitable[empty_pb2.Empty]]:
        r"""Return a callable for the delete chunk method over gRPC.

        Deletes a ``Chunk``.

        Returns:
            Callable[[~.DeleteChunkRequest],
                    Awaitable[~.Empty]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "delete_chunk" not in self._stubs:
            self._stubs["delete_chunk"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/DeleteChunk",
                request_serializer=retriever_service.DeleteChunkRequest.serialize,
                response_deserializer=empty_pb2.Empty.FromString,
            )
        return self._stubs["delete_chunk"]

    @property
    def batch_delete_chunks(
        self,
    ) -> Callable[
        [retriever_service.BatchDeleteChunksRequest], Awaitable[empty_pb2.Empty]
    ]:
        r"""Return a callable for the batch delete chunks method over gRPC.

        Batch delete ``Chunk``\ s.

        Returns:
            Callable[[~.BatchDeleteChunksRequest],
                    Awaitable[~.Empty]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "batch_delete_chunks" not in self._stubs:
            self._stubs["batch_delete_chunks"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/BatchDeleteChunks",
                request_serializer=retriever_service.BatchDeleteChunksRequest.serialize,
                response_deserializer=empty_pb2.Empty.FromString,
            )
        return self._stubs["batch_delete_chunks"]

    @property
    def list_chunks(
        self,
    ) -> Callable[
        [retriever_service.ListChunksRequest],
        Awaitable[retriever_service.ListChunksResponse],
    ]:
        r"""Return a callable for the list chunks method over gRPC.

        Lists all ``Chunk``\ s in a ``Document``.

        Returns:
            Callable[[~.ListChunksRequest],
                    Awaitable[~.ListChunksResponse]]:
                A function that, when called, will call the underlying RPC
                on the server.
        """
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "list_chunks" not in self._stubs:
            self._stubs["list_chunks"] = self._logged_channel.unary_unary(
                "/google.ai.generativelanguage.v1alpha.RetrieverService/ListChunks",
                request_serializer=retriever_service.ListChunksRequest.serialize,
                response_deserializer=retriever_service.ListChunksResponse.deserialize,
            )
        return self._stubs["list_chunks"]

    def _prep_wrapped_messages(self, client_info):
        """Precompute the wrapped methods, overriding the base class method to use async wrappers."""
        self._wrapped_methods = {
            self.create_corpus: self._wrap_method(
                self.create_corpus,
                default_timeout=None,
                client_info=client_info,
            ),
            self.get_corpus: self._wrap_method(
                self.get_corpus,
                default_timeout=None,
                client_info=client_info,
            ),
            self.update_corpus: self._wrap_method(
                self.update_corpus,
                default_timeout=None,
                client_info=client_info,
            ),
            self.delete_corpus: self._wrap_method(
                self.delete_corpus,
                default_timeout=None,
                client_info=client_info,
            ),
            self.list_corpora: self._wrap_method(
                self.list_corpora,
                default_timeout=None,
                client_info=client_info,
            ),
            self.query_corpus: self._wrap_method(
                self.query_corpus,
                default_timeout=None,
                client_info=client_info,
            ),
            self.create_document: self._wrap_method(
                self.create_document,
                default_timeout=None,
                client_info=client_info,
            ),
            self.get_document: self._wrap_method(
                self.get_document,
                default_timeout=None,
                client_info=client_info,
            ),
            self.update_document: self._wrap_method(
                self.update_document,
                default_timeout=None,
                client_info=client_info,
            ),
            self.delete_document: self._wrap_method(
                self.delete_document,
                default_timeout=None,
                client_info=client_info,
            ),
            self.list_documents: self._wrap_method(
                self.list_documents,
                default_timeout=None,
                client_info=client_info,
            ),
            self.query_document: self._wrap_method(
                self.query_document,
                default_timeout=None,
                client_info=client_info,
            ),
            self.create_chunk: self._wrap_method(
                self.create_chunk,
                default_timeout=None,
                client_info=client_info,
            ),
            self.batch_create_chunks: self._wrap_method(
                self.batch_create_chunks,
                default_timeout=None,
                client_info=client_info,
            ),
            self.get_chunk: self._wrap_method(
                self.get_chunk,
                default_timeout=None,
                client_info=client_info,
            ),
            self.update_chunk: self._wrap_method(
                self.update_chunk,
                default_timeout=None,
                client_info=client_info,
            ),
            self.batch_update_chunks: self._wrap_method(
                self.batch_update_chunks,
                default_timeout=None,
                client_info=client_info,
            ),
            self.delete_chunk: self._wrap_method(
                self.delete_chunk,
                default_timeout=None,
                client_info=client_info,
            ),
            self.batch_delete_chunks: self._wrap_method(
                self.batch_delete_chunks,
                default_timeout=None,
                client_info=client_info,
            ),
            self.list_chunks: self._wrap_method(
                self.list_chunks,
                default_timeout=None,
                client_info=client_info,
            ),
            self.get_operation: self._wrap_method(
                self.get_operation,
                default_timeout=None,
                client_info=client_info,
            ),
            self.list_operations: self._wrap_method(
                self.list_operations,
                default_timeout=None,
                client_info=client_info,
            ),
        }

    def _wrap_method(self, func, *args, **kwargs):
        if self._wrap_with_kind:  # pragma: NO COVER
            kwargs["kind"] = self.kind
        return gapic_v1.method_async.wrap_method(func, *args, **kwargs)

    def close(self):
        return self._logged_channel.close()

    @property
    def kind(self) -> str:
        return "grpc_asyncio"

    @property
    def get_operation(
        self,
    ) -> Callable[[operations_pb2.GetOperationRequest], operations_pb2.Operation]:
        r"""Return a callable for the get_operation method over gRPC."""
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "get_operation" not in self._stubs:
            self._stubs["get_operation"] = self._logged_channel.unary_unary(
                "/google.longrunning.Operations/GetOperation",
                request_serializer=operations_pb2.GetOperationRequest.SerializeToString,
                response_deserializer=operations_pb2.Operation.FromString,
            )
        return self._stubs["get_operation"]

    @property
    def list_operations(
        self,
    ) -> Callable[
        [operations_pb2.ListOperationsRequest], operations_pb2.ListOperationsResponse
    ]:
        r"""Return a callable for the list_operations method over gRPC."""
        # Generate a "stub function" on-the-fly which will actually make
        # the request.
        # gRPC handles serialization and deserialization, so we just need
        # to pass in the functions for each.
        if "list_operations" not in self._stubs:
            self._stubs["list_operations"] = self._logged_channel.unary_unary(
                "/google.longrunning.Operations/ListOperations",
                request_serializer=operations_pb2.ListOperationsRequest.SerializeToString,
                response_deserializer=operations_pb2.ListOperationsResponse.FromString,
            )
        return self._stubs["list_operations"]


__all__ = ("RetrieverServiceGrpcAsyncIOTransport",)
