# -*- coding: utf-8 -*-

# Copyright 2021 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Typing description for the WSGI App callables
# For more information on WSGI, see PEP 3333

from typing import Any, Dict, Text, Callable

# Contain CGI environment variables, as defined by the Common Gateway Interface
# specification.
Environment = Dict[Text, Any]

# Used to begin the HTTP response.
StartResponse = Callable[..., Callable[[bytes], None]]
