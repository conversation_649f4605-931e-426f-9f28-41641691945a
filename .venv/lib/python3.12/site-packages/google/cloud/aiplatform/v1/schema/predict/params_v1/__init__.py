# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from google.cloud.aiplatform.v1.schema.predict.params_v1 import (
    gapic_version as package_version,
)

__version__ = package_version.__version__


from .types.image_classification import ImageClassificationPredictionParams
from .types.image_object_detection import ImageObjectDetectionPredictionParams
from .types.image_segmentation import ImageSegmentationPredictionParams
from .types.video_action_recognition import VideoActionRecognitionPredictionParams
from .types.video_classification import VideoClassificationPredictionParams
from .types.video_object_tracking import VideoObjectTrackingPredictionParams

__all__ = (
    "ImageClassificationPredictionParams",
    "ImageObjectDetectionPredictionParams",
    "ImageSegmentationPredictionParams",
    "VideoActionRecognitionPredictionParams",
    "VideoClassificationPredictionParams",
    "VideoObjectTrackingPredictionParams",
)
