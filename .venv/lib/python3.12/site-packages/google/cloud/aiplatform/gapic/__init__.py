# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# The latest GAPIC version is exported to the google.cloud.aiplatform.gapic namespace.
from google.cloud.aiplatform_v1 import *
from google.cloud.aiplatform.gapic import schema

from google.cloud import aiplatform_v1 as v1

__all__ = ()
