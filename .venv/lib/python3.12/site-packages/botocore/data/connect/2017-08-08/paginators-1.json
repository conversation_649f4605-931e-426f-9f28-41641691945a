{"pagination": {"GetMetricData": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "MetricResults"}, "ListRoutingProfiles": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "RoutingProfileSummaryList"}, "ListSecurityProfiles": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "SecurityProfileSummaryList"}, "ListUserHierarchyGroups": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "UserHierarchyGroupSummaryList"}, "ListUsers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "UserSummaryList"}, "ListContactFlows": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ContactFlowSummaryList"}, "ListHoursOfOperations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "HoursOfOperationSummaryList"}, "ListPhoneNumbers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "PhoneNumberSummaryList"}, "ListQueues": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "QueueSummaryList"}, "ListPrompts": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "PromptSummaryList"}, "ListRoutingProfileQueues": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "RoutingProfileQueueConfigSummaryList", "non_aggregate_keys": ["LastModifiedRegion", "LastModifiedTime"]}, "ListApprovedOrigins": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Origins"}, "ListInstanceAttributes": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Attributes"}, "ListInstanceStorageConfigs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "StorageConfigs"}, "ListInstances": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "InstanceSummaryList"}, "ListLambdaFunctions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "LambdaFunctions"}, "ListLexBots": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "LexBots"}, "ListSecurityKeys": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "SecurityKeys"}, "ListIntegrationAssociations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "IntegrationAssociationSummaryList"}, "ListUseCases": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "UseCaseSummaryList"}, "ListQuickConnects": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "QuickConnectSummaryList"}, "ListQueueQuickConnects": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "QuickConnectSummaryList", "non_aggregate_keys": ["LastModifiedRegion", "LastModifiedTime"]}, "ListBots": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "LexBots"}, "ListAgentStatuses": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AgentStatusSummaryList"}, "ListSecurityProfilePermissions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Permissions", "non_aggregate_keys": ["LastModifiedRegion", "LastModifiedTime"]}, "ListContactReferences": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "ReferenceSummaryList"}, "ListContactFlowModules": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ContactFlowModulesSummaryList"}, "ListDefaultVocabularies": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DefaultVocabularyList"}, "SearchVocabularies": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "VocabularySummaryList"}, "ListPhoneNumbersV2": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ListPhoneNumbersSummaryList"}, "SearchAvailablePhoneNumbers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AvailableNumbersList"}, "SearchUsers": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "Users"}, "ListTaskTemplates": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TaskTemplates"}, "SearchSecurityProfiles": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "SecurityProfiles"}, "SearchQueues": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "Queues"}, "SearchRoutingProfiles": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "RoutingProfiles"}, "ListTrafficDistributionGroups": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TrafficDistributionGroupSummaryList"}, "ListRules": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "RuleSummaryList"}, "ListContactEvaluations": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "EvaluationSummaryList"}, "ListEvaluationFormVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "EvaluationFormVersionSummaryList"}, "ListEvaluationForms": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "EvaluationFormSummaryList"}, "SearchHoursOfOperations": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "HoursOfOperations"}, "SearchPrompts": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "Prompts"}, "SearchQuickConnects": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "QuickConnects"}, "SearchResourceTags": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Tags"}, "ListTrafficDistributionGroupUsers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TrafficDistributionGroupUserSummaryList"}, "ListViewVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ViewVersionSummaryList"}, "ListViews": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ViewsSummaryList"}, "ListSecurityProfileApplications": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Applications", "non_aggregate_keys": ["LastModifiedRegion", "LastModifiedTime"]}, "ListFlowAssociations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "FlowAssociationSummaryList"}, "ListPredefinedAttributes": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "PredefinedAttributeSummaryList"}, "ListUserProficiencies": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["LastModifiedTime", "LastModifiedRegion"], "output_token": "NextToken", "result_key": "UserProficiencyList"}, "SearchContacts": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["TotalCount"], "output_token": "NextToken", "result_key": "Contacts"}, "SearchPredefinedAttributes": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "PredefinedAttributes"}, "SearchContactFlowModules": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "ContactFlowModules"}, "SearchContactFlows": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "ContactFlows"}, "ListAuthenticationProfiles": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AuthenticationProfileSummaryList"}, "SearchAgentStatuses": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "AgentStatuses"}, "SearchUserHierarchyGroups": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "UserHierarchyGroups"}, "ListContactFlowVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ContactFlowVersionSummaryList"}, "ListHoursOfOperationOverrides": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["LastModifiedRegion", "LastModifiedTime"], "output_token": "NextToken", "result_key": "HoursOfOperationOverrideList"}, "SearchHoursOfOperationOverrides": {"input_token": "NextToken", "limit_key": "MaxResults", "non_aggregate_keys": ["ApproximateTotalCount"], "output_token": "NextToken", "result_key": "HoursOfOperationOverrides"}}}