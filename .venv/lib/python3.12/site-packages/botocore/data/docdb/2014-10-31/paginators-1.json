{"pagination": {"DescribeCertificates": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Certificates"}, "DescribeDBClusterParameterGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterParameterGroups"}, "DescribeDBClusterParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Parameters"}, "DescribeDBClusterSnapshots": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterSnapshots"}, "DescribeDBClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusters"}, "DescribeDBEngineVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBEngineVersions"}, "DescribeDBInstances": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBInstances"}, "DescribeDBSubnetGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBSubnetGroups"}, "DescribeEvents": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Events"}, "DescribeOrderableDBInstanceOptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "OrderableDBInstanceOptions"}, "DescribePendingMaintenanceActions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "PendingMaintenanceActions"}, "DescribeEventSubscriptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "EventSubscriptionsList"}, "DescribeGlobalClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "GlobalClusters"}}}