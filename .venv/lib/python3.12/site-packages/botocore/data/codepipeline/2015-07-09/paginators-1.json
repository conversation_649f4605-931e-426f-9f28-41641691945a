{"pagination": {"ListActionTypes": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "actionTypes"}, "ListPipelineExecutions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "pipelineExecutionSummaries"}, "ListPipelines": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "pipelines", "limit_key": "maxResults"}, "ListWebhooks": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "webhooks"}, "ListActionExecutions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "actionExecutionDetails"}, "ListTagsForResource": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "tags"}, "ListRuleExecutions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "ruleExecutionDetails"}, "ListDeployActionExecutionTargets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "targets"}}}